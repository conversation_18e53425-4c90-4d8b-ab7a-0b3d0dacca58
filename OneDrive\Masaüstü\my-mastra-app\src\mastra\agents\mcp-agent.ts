import 'dotenv/config';
import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { mcpTools } from '../tools/mcp-tools.js';

export const mcpAgent = new Agent({
  name: 'mcp-agent',
  instructions: `Sen bir MCP (Model Context Protocol) agent'ısın.
  Kullanıcıların sorularını yanıtlamak için MCP araçlarını kullanabilirsin.
  Kullanıcı ile Türkçe konuş ve onlara yardımcı ol.
  MCP araçlarını kullanarak gerekli bilgileri al ve kullanıcıya net, anlaşılır cevaplar ver.`,
  model: {
    provider: openai({
      apiKey: process.env.OPENAI_API_KEY,
    }),
    name: 'gpt-4o-mini',
    toolChoice: 'auto',
  },
  tools: mcpTools,
});
