import { createTool } from '@mastra/core';
import { z } from 'zod';

// MCP Server URL'i
const MCP_SERVER_URL = 'https://server.smithery.ai/@meren41/s-navmobil/mcp';
const MCP_API_KEY = '02050232-0fc4-4ada-91d7-aa175ae658a7';

// MCP API'sine istek gönderen yardımcı fonksiyon
async function callMCPAPI(method: string, params: any = {}) {
  try {
    const response = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${MCP_API_KEY}`,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: Date.now(),
        method: method,
        params: params,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.result || data;
  } catch (error) {
    console.error('MCP API call failed:', error);
    throw error;
  }
}

// MCP araçlarını listeleyen tool
export const listMCPTools = createTool({
  id: 'list-mcp-tools',
  description: 'MCP sunucusundaki mevcut araçları listeler',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      const result = await callMCPAPI('tools/list');
      return {
        success: true,
        tools: result.tools || [],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata',
      };
    }
  },
});

// MCP aracını çağıran genel tool
export const callMCPTool = createTool({
  id: 'call-mcp-tool',
  description: 'MCP sunucusundaki belirli bir aracı çağırır',
  inputSchema: z.object({
    toolName: z.string().describe('Çağrılacak aracın adı'),
    arguments: z.record(z.any()).optional().describe('Araca gönderilecek parametreler'),
  }),
  execute: async ({ toolName, arguments: args = {} }) => {
    try {
      const result = await callMCPAPI('tools/call', {
        name: toolName,
        arguments: args,
      });
      return {
        success: true,
        result: result,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata',
      };
    }
  },
});

// MCP kaynaklarını listeleyen tool
export const listMCPResources = createTool({
  id: 'list-mcp-resources',
  description: 'MCP sunucusundaki mevcut kaynakları listeler',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      const result = await callMCPAPI('resources/list');
      return {
        success: true,
        resources: result.resources || [],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata',
      };
    }
  },
});

// MCP kaynağını okuyan tool
export const readMCPResource = createTool({
  id: 'read-mcp-resource',
  description: 'MCP sunucusundaki belirli bir kaynağı okur',
  inputSchema: z.object({
    uri: z.string().describe('Okunacak kaynağın URI\'si'),
  }),
  execute: async ({ uri }) => {
    try {
      const result = await callMCPAPI('resources/read', {
        uri: uri,
      });
      return {
        success: true,
        content: result.contents || result,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata',
      };
    }
  },
});

// Tüm MCP araçlarını export et
export const mcpTools = [
  listMCPTools,
  callMCPTool,
  listMCPResources,
  readMCPResource,
];
